{"version": 3, "file": "use-native.js", "sourceRoot": "", "sources": ["../../src/use-native.ts"], "names": [], "mappings": "AACA,OAAO,QAAQ,MAAM,eAAe,CAAA;AAEpC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,OAAO,CAAC,OAAO,CAAA;AAC9E,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AAEpD,qBAAqB;AACrB,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAChE,oBAAoB;AACpB,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC,CAAA;AAE7D,oEAAoE;AACpE,yEAAyE;AACzE,MAAM,CAAC,MAAM,SAAS,GACpB,CAAC,SAAS,IAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;IAClC,GAAG,EAAE,CAAC,KAAK;IACb,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,CAAA;AACvC,MAAM,CAAC,MAAM,aAAa,GACxB,CAAC,SAAS,IAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;IAClC,GAAG,EAAE,CAAC,KAAK;IACb,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,CAAA", "sourcesContent": ["import { RimrafAsyncOptions, RimrafOptions } from './index.js'\nimport platform from './platform.js'\n\nconst version = process.env.__TESTING_RIMRAF_NODE_VERSION__ || process.version\nconst versArr = version.replace(/^v/, '').split('.')\n\n/* c8 ignore start */\nconst [major = 0, minor = 0] = versArr.map(v => parseInt(v, 10))\n/* c8 ignore stop */\nconst hasNative = major > 14 || (major === 14 && minor >= 14)\n\n// we do NOT use native by default on Windows, because Node's native\n// rm implementation is less advanced.  Change this code if that changes.\nexport const useNative: (opt?: RimrafAsyncOptions) => boolean =\n  !hasNative || platform === 'win32' ?\n    () => false\n  : opt => !opt?.signal && !opt?.filter\nexport const useNativeSync: (opt?: RimrafOptions) => boolean =\n  !hasNative || platform === 'win32' ?\n    () => false\n  : opt => !opt?.signal && !opt?.filter\n"]}