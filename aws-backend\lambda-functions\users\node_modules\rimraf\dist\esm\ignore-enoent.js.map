{"version": 3, "file": "ignore-enoent.js", "sourceRoot": "", "sources": ["../../src/ignore-enoent.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,YAAY,GAAG,KAAK,EAAE,CAAe,EAAE,EAAE,CACpD,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;IACX,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACzB,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAC,CAAA;AAEJ,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,EAAa,EAAE,EAAE;IAChD,IAAI,CAAC;QACH,OAAO,EAAE,EAAE,CAAA;IACb,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YACrD,MAAM,EAAE,CAAA;QACV,CAAC;IACH,CAAC;AACH,CAAC,CAAA", "sourcesContent": ["export const ignoreENOENT = async (p: Promise<any>) =>\n  p.catch(er => {\n    if (er.code !== 'ENOENT') {\n      throw er\n    }\n  })\n\nexport const ignoreENOENTSync = (fn: () => any) => {\n  try {\n    return fn()\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code !== 'ENOENT') {\n      throw er\n    }\n  }\n}\n"]}