{"version": 3, "file": "handler.js", "sourceRoot": "", "sources": ["../src/handler.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,gGAA0G;AAC1G,8DAA0D;AAC1D,wDAAiI;AAEjI,cAAc;AACd,MAAM,aAAa,GAAG,IAAI,gEAA6B,CAAC;IACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,wBAAwB;IAClE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW;IACrD,WAAW,EAAE;QACT,WAAW,EAAE,MAAM;QACnB,eAAe,EAAE,MAAM;KAC1B;CACJ,CAAC,CAAC;AAEH,yBAAyB;AACzB,MAAM,YAAY,GAAG,IAAI,gCAAc,CAAC;IACpC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,wBAAwB;IAClE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW;IACrD,WAAW,EAAE;QACT,WAAW,EAAE,MAAM;QACnB,eAAe,EAAE,MAAM;KAC1B;CACJ,CAAC,CAAC;AAEH,MAAM,SAAS,GAAG,qCAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAE5D,cAAc;AACd,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,MAAM,mBAAmB,GAAG,cAAc,CAAC;AAC3C,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,MAAM,aAAa,GAAG,SAAS,CAAC;AAmChC,eAAe;AACf,MAAM,WAAW,GAAG;IAChB,6BAA6B,EAAE,GAAG;IAClC,8BAA8B,EAAE,sEAAsE;IACtG,8BAA8B,EAAE,6BAA6B;IAC7D,kCAAkC,EAAE,MAAM;CAC7C,CAAC;AAEF,SAAS,kBAAkB,CAAC,UAAkB,EAAE,IAAS;IACrD,OAAO;QACH,UAAU;QACV,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC7B,CAAC;AACN,CAAC;AAED,iCAAiC;AACjC,SAAS,sBAAsB,CAAC,UAAkB;IAC9C,IAAI,CAAC;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClF,OAAO,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,yBAAyB;AACzB,KAAK,UAAU,kBAAkB,CAAC,aAAqB;IACnD,IAAI,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YAChD,SAAS,EAAE,WAAW;YACtB,gBAAgB,EAAE,kCAAkC;YACpD,yBAAyB,EAAE;gBACvB,gBAAgB,EAAE,aAAa;aAClC;SACJ,CAAC,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAS,CAAC,CAAC,CAAC,IAAI,CAAC;IACpF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,iBAAiB;AACjB,KAAK,UAAU,WAAW,CAAC,MAAc;IACrC,IAAI,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,yBAAU,CAAC;YAC/C,SAAS,EAAE,WAAW;YACtB,GAAG,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;SACtB,CAAC,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC,IAAY,IAAI,IAAI,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,mBAAmB;AACnB,KAAK,UAAU,cAAc,CAAC,MAAc;IACxC,IAAI,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,yBAAU,CAAC;YAC/C,SAAS,EAAE,mBAAmB;YAC9B,GAAG,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;SAC3B,CAAC,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC,IAAmB,IAAI,IAAI,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,sBAAsB;AACtB,KAAK,UAAU,YAAY,CAAC,MAAc;IACtC,IAAI,CAAC;QACD,kBAAkB;QAClB,MAAM,WAAW,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YACrD,SAAS,EAAE,WAAW;YACtB,gBAAgB,EAAE,6CAA6C;YAC/D,yBAAyB,EAAE;gBACvB,SAAS,EAAE,MAAM;gBACjB,WAAW,EAAE,IAAI;aACpB;YACD,MAAM,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,6CAA6C;QAC7C,MAAM,WAAW,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YACrD,SAAS,EAAE,WAAW;YACtB,gBAAgB,EAAE,mBAAmB;YACrC,yBAAyB,EAAE;gBACvB,SAAS,EAAE,MAAM;aACpB;YACD,MAAM,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,sBAAsB;QACtB,MAAM,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YACzD,SAAS,EAAE,aAAa;YACxB,gBAAgB,EAAE,wBAAwB;YAC1C,yBAAyB,EAAE;gBACvB,SAAS,EAAE,MAAM;aACpB;YACD,MAAM,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,sBAAsB;QACtB,MAAM,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YACzD,SAAS,EAAE,aAAa;YACxB,gBAAgB,EAAE,uBAAuB;YACzC,yBAAyB,EAAE;gBACvB,SAAS,EAAE,MAAM;aACpB;YACD,MAAM,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACH,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC;YAC7B,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC;YAC7B,SAAS,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC;YACrC,SAAS,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC;SACxC,CAAC;IACN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;IAC9D,CAAC;AACL,CAAC;AAED,iBAAiB;AACjB,KAAK,UAAU,YAAY,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE,SAAiB,CAAC;IAC9E,IAAI,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,0BAAW,CAAC;YAChD,SAAS,EAAE,WAAW;YACtB,gBAAgB,EAAE,6CAA6C;YAC/D,yBAAyB,EAAE;gBACvB,SAAS,EAAE,MAAM;gBACjB,WAAW,EAAE,IAAI;aACpB;SACJ,CAAC,CAAC,CAAC;QAEJ,qDAAqD;QACrD,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;aAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;aACnF,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;QAEnC,OAAO,KAAK,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,EAAE,CAAC;IACd,CAAC;AACL,CAAC;AAEM,MAAM,OAAO,GAAG,KAAK,EAAE,KAA2B,EAAE,OAAgB,EAAkC,EAAE;IAC3G,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAErE,wBAAwB;IACxB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IACxB,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;IAChC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC;IAE9E,IAAI,CAAC;QACD,wDAAwD;QACxD,IAAI,aAAa,GAAkB,IAAI,CAAC;QACxC,IAAI,UAAU,EAAE,CAAC;YACb,aAAa,GAAG,sBAAsB,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,EAAE,CAAC;YAC/D,8BAA8B;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;YAEtC,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,CAAC;YAEzC,OAAO,kBAAkB,CAAC,GAAG,EAAE;gBAC3B,IAAI;gBACJ,OAAO;gBACP,KAAK;aACR,CAAC,CAAC;QACP,CAAC;QAED,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE,CAAC;YAC7D,4BAA4B;YAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC;YACnE,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC;YAEpE,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAExD,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;YAChD,qDAAqD;YACrD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,aAAa,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;YAC5C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAErC,qBAAqB;YACrB,MAAM,WAAW,GAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS;gBAAE,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YAClF,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS;gBAAE,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACtE,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS;gBAAE,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YACvD,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS;gBAAE,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAE5E,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,4BAA4B;gBACnE,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,4BAAa,CAAC;oBACnC,SAAS,EAAE,WAAW;oBACtB,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;oBACpB,gBAAgB,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC9F,wBAAwB,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBACnE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;wBACrB,OAAO,GAAG,CAAC;oBACf,CAAC,EAAE,EAAS,CAAC;oBACb,yBAAyB,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBACpE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;wBAClC,OAAO,GAAG,CAAC;oBACf,CAAC,EAAE,EAAS,CAAC;iBAChB,CAAC,CAAC,CAAC;YACR,CAAC;YAED,sBAAsB;YACtB,MAAM,cAAc,GAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS;gBAAE,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/E,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;gBAAE,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAC5E,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;gBAAE,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YACtE,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS;gBAAE,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACzE,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS;gBAAE,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzE,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,4BAA4B;gBACtE,0BAA0B;gBAC1B,MAAM,eAAe,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEtD,IAAI,eAAe,EAAE,CAAC;oBAClB,0BAA0B;oBAC1B,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,4BAAa,CAAC;wBACnC,SAAS,EAAE,mBAAmB;wBAC9B,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;wBACzB,gBAAgB,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACjG,wBAAwB,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;4BACtE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;4BACrB,OAAO,GAAG,CAAC;wBACf,CAAC,EAAE,EAAS,CAAC;wBACb,yBAAyB,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;4BACvE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;4BACrC,OAAO,GAAG,CAAC;wBACf,CAAC,EAAE,EAAS,CAAC;qBAChB,CAAC,CAAC,CAAC;gBACR,CAAC;qBAAM,CAAC;oBACJ,qBAAqB;oBACrB,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,yBAAU,CAAC;wBAChC,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE;4BACF,OAAO,EAAE,IAAI,CAAC,EAAE;4BAChB,UAAU,EAAE,GAAG;4BACf,GAAG,cAAc;yBACpB;qBACJ,CAAC,CAAC,CAAC;gBACR,CAAC;YACL,CAAC;YAED,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,kBAAkB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAEpE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,kBAAkB,CAAC,GAAG,EAAE;YAC3B,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SACpE,CAAC,CAAC;IACP,CAAC;AACL,CAAC,CAAC;AA3IW,QAAA,OAAO,WA2IlB"}