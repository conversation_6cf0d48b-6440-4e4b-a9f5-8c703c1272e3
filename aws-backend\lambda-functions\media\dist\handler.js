"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_s3_1 = require("@aws-sdk/client-s3");
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const sharp_1 = __importDefault(require("sharp"));
const s3Client = new client_s3_1.S3Client({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    },
    forcePathStyle: true
});
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';
const AVATARS_BUCKET = process.env.S3_BUCKET_AVATARS || 'gameflex-avatars-development';
const TEMP_BUCKET = process.env.S3_BUCKET_TEMP || 'gameflex-temp-development';
const USERS_TABLE = 'Users';
const MEDIA_TABLE = 'Media';
const CHANNEL_MEMBERS_TABLE = 'ChannelMembers';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '52428800');
const ALLOWED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
const ALLOWED_VIDEO_TYPES = ['mp4', 'mov', 'avi', 'webm'];
function createCorsResponse(statusCode, body) {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}
async function getUserFromToken(accessToken) {
    try {
        const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand({
            AccessToken: accessToken
        });
        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username;
        const result = await docClient.send(new lib_dynamodb_1.QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'cognito_user_id-index',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            FilterExpression: 'is_active = :isActive',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId,
                ':isActive': true
            }
        }));
        return result.Items && result.Items.length > 0 ? result.Items[0] : null;
    }
    catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}
function getFileType(filename) {
    const extension = filename.toLowerCase().split('.').pop() || '';
    if (ALLOWED_IMAGE_TYPES.includes(extension)) {
        return 'image';
    }
    else if (ALLOWED_VIDEO_TYPES.includes(extension)) {
        return 'video';
    }
    else {
        return 'unknown';
    }
}
function getMimeType(extension) {
    const mimeTypes = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'mp4': 'video/mp4',
        'mov': 'video/quicktime',
        'avi': 'video/x-msvideo',
        'webm': 'video/webm'
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}
async function processImage(imageData) {
    try {
        const image = (0, sharp_1.default)(imageData);
        const metadata = await image.metadata();
        let { width = 0, height = 0 } = metadata;
        const maxSize = 2048;
        if (width > maxSize || height > maxSize) {
            const resized = await image
                .resize(maxSize, maxSize, {
                fit: 'inside',
                withoutEnlargement: true
            })
                .jpeg({ quality: 85 })
                .toBuffer();
            const resizedMetadata = await (0, sharp_1.default)(resized).metadata();
            width = resizedMetadata.width || 0;
            height = resizedMetadata.height || 0;
            return {
                data: resized,
                width,
                height,
                size: resized.length
            };
        }
        else {
            const processed = await image
                .jpeg({ quality: 85 })
                .toBuffer();
            return {
                data: processed,
                width,
                height,
                size: processed.length
            };
        }
    }
    catch (error) {
        console.error('Image processing failed:', error);
        throw new Error('Image processing failed');
    }
}
async function uploadMediaHandler(event) {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';
        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }
        const accessToken = authHeader.substring(7);
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }
        const body = JSON.parse(event.body || '{}');
        const filename = body.filename || '';
        const fileDataB64 = body.file_data || '';
        const channelId = body.channel_id;
        if (!filename || !fileDataB64) {
            return createCorsResponse(400, {
                error: 'Filename and file data are required'
            });
        }
        let fileData;
        try {
            fileData = Buffer.from(fileDataB64, 'base64');
        }
        catch (error) {
            return createCorsResponse(400, {
                error: 'Invalid file data encoding'
            });
        }
        if (fileData.length > MAX_FILE_SIZE) {
            return createCorsResponse(400, {
                error: `File size exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`
            });
        }
        const extension = filename.toLowerCase().split('.').pop() || '';
        const fileType = getFileType(filename);
        if (fileType === 'unknown') {
            return createCorsResponse(400, {
                error: 'Unsupported file type'
            });
        }
        if (channelId) {
            const result = await docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channel_id: channelId,
                    user_id: user.id
                }
            }));
            if (!result.Item) {
                return createCorsResponse(403, {
                    error: 'You are not a member of this channel'
                });
            }
        }
        let processedData = fileData;
        let width;
        let height;
        if (fileType === 'image') {
            try {
                const processed = await processImage(fileData);
                processedData = processed.data;
                width = processed.width;
                height = processed.height;
            }
            catch (error) {
                return createCorsResponse(400, {
                    error: `Image processing failed: ${error}`
                });
            }
        }
        const mediaId = (0, uuid_1.v4)();
        const timestamp = Date.now();
        const uniqueFilename = `${mediaId}_${timestamp}.${extension}`;
        const s3Key = channelId
            ? `user/${user.id}/${channelId}/${uniqueFilename}`
            : `user/${user.id}/${uniqueFilename}`;
        try {
            const putObjectCommand = new client_s3_1.PutObjectCommand({
                Bucket: MEDIA_BUCKET,
                Key: s3Key,
                Body: processedData,
                ContentType: getMimeType(extension),
                Metadata: {
                    'original_filename': filename,
                    'uploaded_by': user.id,
                    'upload_timestamp': timestamp.toString()
                }
            });
            await s3Client.send(putObjectCommand);
        }
        catch (error) {
            console.error('S3 upload failed:', error);
            return createCorsResponse(500, {
                error: 'Failed to upload file'
            });
        }
        const now = new Date().toISOString();
        const media = {
            id: mediaId,
            filename: uniqueFilename.split('.')[0],
            original_filename: filename,
            extension: extension,
            mime_type: getMimeType(extension),
            size_bytes: processedData.length,
            width: width,
            height: height,
            type: fileType,
            s3_bucket: MEDIA_BUCKET,
            s3_key: s3Key,
            location: 'user',
            owner_id: user.id,
            channel_id: channelId,
            is_processed: true,
            processing_status: 'completed',
            created_at: now,
            updated_at: now
        };
        await docClient.send(new lib_dynamodb_1.PutCommand({
            TableName: MEDIA_TABLE,
            Item: media
        }));
        const mediaUrl = `http://localhost:45660/${MEDIA_BUCKET}/${s3Key}`;
        return createCorsResponse(201, {
            message: 'Media uploaded successfully',
            media: {
                id: media.id,
                filename: media.filename,
                extension: media.extension,
                type: media.type,
                width: media.width,
                height: media.height,
                url: mediaUrl,
                created_at: media.created_at
            }
        });
    }
    catch (error) {
        console.error('Upload media handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function getMediaHandler(event) {
    try {
        const mediaId = event.pathParameters?.id;
        if (!mediaId) {
            return createCorsResponse(400, {
                error: 'Media ID is required'
            });
        }
        const mediaResult = await docClient.send(new lib_dynamodb_1.GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: mediaId }
        }));
        if (!mediaResult.Item) {
            return createCorsResponse(404, {
                error: 'Media not found'
            });
        }
        const media = mediaResult.Item;
        const ownerResult = await docClient.send(new lib_dynamodb_1.GetCommand({
            TableName: USERS_TABLE,
            Key: { id: media.owner_id }
        }));
        const owner = ownerResult.Item;
        const mediaUrl = `http://localhost:45660/${media.s3_bucket}/${media.s3_key}`;
        return createCorsResponse(200, {
            media: {
                id: media.id,
                filename: media.filename,
                original_filename: media.original_filename,
                extension: media.extension,
                mime_type: media.mime_type,
                size_bytes: media.size_bytes,
                width: media.width,
                height: media.height,
                type: media.type,
                url: mediaUrl,
                is_processed: media.is_processed,
                processing_status: media.processing_status,
                created_at: media.created_at,
                owner: {
                    id: media.owner_id,
                    username: owner?.username || 'Unknown',
                    display_name: owner?.display_name || 'Unknown User'
                }
            }
        });
    }
    catch (error) {
        console.error('Get media handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
const handler = async (event, context) => {
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }
    const path = event.path;
    const method = event.httpMethod;
    try {
        if (path === '/media/upload' && method === 'POST') {
            return await uploadMediaHandler(event);
        }
        else if (path.startsWith('/media/') && method === 'GET') {
            return await getMediaHandler(event);
        }
        else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    }
    catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
exports.handler = handler;
