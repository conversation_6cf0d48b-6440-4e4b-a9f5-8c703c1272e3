#!/bin/bash

# GameFlex Lambda Function Builder Script
# This script builds all TypeScript Lambda functions and creates deployment packages

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[LAMBDA-BUILDER]${NC} $1"
}

# Function to build a single Lambda function
build_lambda_function() {
    local function_name=$1
    local function_dir=$2
    
    log_info "Building Lambda function: $function_name"
    
    # Check if function directory exists
    if [ ! -d "/workspace/lambda-functions/$function_dir" ]; then
        log_warn "Lambda function directory not found: /workspace/lambda-functions/$function_dir"
        return 0
    fi
    
    cd "/workspace/lambda-functions/$function_dir"
    
    # Clean previous builds
    log_info "Cleaning previous builds for $function_name..."
    rm -rf dist node_modules package-lock.json *.zip
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        log_error "package.json not found for $function_name"
        return 1
    fi
    
    # Install all dependencies (including devDependencies for building)
    log_info "Installing all dependencies for $function_name..."
    npm ci --silent || npm install --silent
    
    # Build TypeScript
    log_info "Building TypeScript for $function_name..."
    if [ -f "tsconfig.json" ]; then
        npx tsc
    elif npm run build 2>/dev/null; then
        log_info "Used npm run build for $function_name"
    else
        log_warn "No tsconfig.json or build script found for $function_name, using default TypeScript compilation"
        npx tsc --target es2020 --module commonjs --outDir dist --rootDir src --strict --esModuleInterop --skipLibCheck src/*.ts
    fi
    
    if [ $? -ne 0 ]; then
        log_error "TypeScript build failed for $function_name"
        return 1
    fi
    
    # Verify dist directory was created
    if [ ! -d "dist" ]; then
        log_error "Build output directory 'dist' not found for $function_name"
        return 1
    fi
    
    # Create deployment package with proper structure
    log_info "Creating deployment package for $function_name..."
    
    # Create temporary directory for packaging
    local temp_dir="/tmp/${function_name}-package"
    rm -rf "$temp_dir"
    mkdir -p "$temp_dir"
    
    # Copy built JavaScript files
    cp -r dist/* "$temp_dir/"
    
    # Copy package.json for production dependency installation
    cp package.json "$temp_dir/"
    [ -f "package-lock.json" ] && cp package-lock.json "$temp_dir/"
    
    # Install production dependencies in temp directory
    cd "$temp_dir"
    log_info "Installing production dependencies for $function_name..."
    npm ci --production --silent || npm install --production --silent
    
    # Remove package files and unnecessary files
    rm -f package.json package-lock.json
    find . -name "*.map" -delete
    find . -name "*.d.ts" -delete
    find . -name "*.ts" -delete
    find . -name "tsconfig.json" -delete
    
    # Create zip package
    zip -r "/workspace/packages/${function_name}.zip" . -q
    
    # Clean up temporary directory
    rm -rf "$temp_dir"
    
    # Verify package was created and show size
    if [ -f "/workspace/packages/${function_name}.zip" ]; then
        local package_size=$(du -h "/workspace/packages/${function_name}.zip" | cut -f1)
        log_info "Successfully built Lambda function: $function_name (${package_size})"
    else
        log_error "Failed to create package for $function_name"
        return 1
    fi
    
    cd /workspace
}

# Main execution
log_header "Starting Lambda function build process..."

# Create packages directory if it doesn't exist
mkdir -p /workspace/packages

# Clean previous packages
log_info "Cleaning previous packages..."
rm -f /workspace/packages/*.zip

# Build all Lambda functions
log_info "Building all Lambda functions..."

# Track build results
declare -a successful_builds=()
declare -a failed_builds=()

# Build auth function
if build_lambda_function "gameflex-auth-development" "auth"; then
    successful_builds+=("auth")
else
    failed_builds+=("auth")
fi

# Build posts function
if build_lambda_function "gameflex-posts-development" "posts"; then
    successful_builds+=("posts")
else
    failed_builds+=("posts")
fi

# Build users function
if build_lambda_function "gameflex-users-development" "users"; then
    successful_builds+=("users")
else
    failed_builds+=("users")
fi

# Build media function
if build_lambda_function "gameflex-media-development" "media"; then
    successful_builds+=("media")
else
    failed_builds+=("media")
fi

# Report results
log_header "Build Summary"
log_info "Successful builds: ${#successful_builds[@]}"
for func in "${successful_builds[@]}"; do
    log_info "  ✓ $func"
done

if [ ${#failed_builds[@]} -gt 0 ]; then
    log_error "Failed builds: ${#failed_builds[@]}"
    for func in "${failed_builds[@]}"; do
        log_error "  ✗ $func"
    done
    exit 1
fi

log_header "All Lambda functions built successfully!"
log_info "Deployment packages created in /workspace/packages/"
ls -la /workspace/packages/
