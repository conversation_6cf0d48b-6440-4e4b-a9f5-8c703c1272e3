{"version": 3, "file": "fix-eperm.js", "sourceRoot": "", "sources": ["../../src/fix-eperm.ts"], "names": [], "mappings": ";;;AAAA,mCAA6C;AAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAQ,CAAA;AAEnB,MAAM,QAAQ,GACnB,CAAC,EAAkC,EAAE,EAAE,CAAC,KAAK,EAAE,IAAY,EAAE,EAAE;IAC7D,IAAI,CAAC;QACH,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,OAAM;QACR,CAAC;QACD,IAAI,GAAG,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YAC1B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,IAAI,GAAG,GAA4B,CAAA;gBACzC,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,OAAM;gBACR,CAAC;gBACD,MAAM,EAAE,CAAA;YACV,CAAC;YACD,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;QACvB,CAAC;QACD,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAvBU,QAAA,QAAQ,YAuBlB;AAEI,MAAM,YAAY,GAAG,CAAC,EAAyB,EAAE,EAAE,CAAC,CAAC,IAAY,EAAE,EAAE;IAC1E,IAAI,CAAC;QACH,OAAO,EAAE,CAAC,IAAI,CAAC,CAAA;IACjB,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,OAAM;QACR,CAAC;QACD,IAAI,GAAG,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,IAAA,iBAAS,EAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YACxB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,IAAI,GAAG,GAA4B,CAAA;gBACzC,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,OAAM;gBACR,CAAC;gBACD,MAAM,EAAE,CAAA;YACV,CAAC;YACD,OAAO,EAAE,CAAC,IAAI,CAAC,CAAA;QACjB,CAAC;QACD,MAAM,EAAE,CAAA;IACV,CAAC;AACH,CAAC,CAAA;AAtBY,QAAA,YAAY,gBAsBxB", "sourcesContent": ["import { chmodSync, promises } from './fs.js'\nconst { chmod } = promises\n\nexport const fixEPERM =\n  (fn: (path: string) => Promise<any>) => async (path: string) => {\n    try {\n      return await fn(path)\n    } catch (er) {\n      const fer = er as NodeJS.ErrnoException\n      if (fer?.code === 'ENOENT') {\n        return\n      }\n      if (fer?.code === 'EPERM') {\n        try {\n          await chmod(path, 0o666)\n        } catch (er2) {\n          const fer2 = er2 as NodeJS.ErrnoException\n          if (fer2?.code === 'ENOENT') {\n            return\n          }\n          throw er\n        }\n        return await fn(path)\n      }\n      throw er\n    }\n  }\n\nexport const fixEPERMSync = (fn: (path: string) => any) => (path: string) => {\n  try {\n    return fn(path)\n  } catch (er) {\n    const fer = er as NodeJS.ErrnoException\n    if (fer?.code === 'ENOENT') {\n      return\n    }\n    if (fer?.code === 'EPERM') {\n      try {\n        chmodSync(path, 0o666)\n      } catch (er2) {\n        const fer2 = er2 as NodeJS.ErrnoException\n        if (fer2?.code === 'ENOENT') {\n          return\n        }\n        throw er\n      }\n      return fn(path)\n    }\n    throw er\n  }\n}\n"]}